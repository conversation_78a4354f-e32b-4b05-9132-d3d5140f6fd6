"""
为agent机器人提供飞书集成工具，用于将SQL查询结果上传到飞书云文档，并根据内容大小动态调整展示的行数。
"""
import os
from datetime import datetime

from src.utils.logger import logger
from src.models.user_info_class import UserInfo
from src.models.query_result import SQLQueryResult
from src.services.feishu.import_csv_to_feishu_bitable import create_bitable_and_upload_csv
from src.services.feishu.drive_service import DriveService

# 配置
min_rows = int(os.getenv("MIN_ROWS_TO_IMPORT", 20))  # 默认最小导入行数为20行
min_rows_to_cutoff = int(os.getenv("MIN_ROWS_TO_CUTOFF", 400))  # 默认最小截断行数为400行
max_content_size = int(os.getenv("MAX_CONTENT_TO_CUTOFF", 16000))  # 默认最大内容大小为16000字节
max_upload_content_size_mb = int(os.getenv("MAX_UPLOAD_CONTENT_SIZE_MB", 20))  # 默认最大上传内容大小为20MB


async def upload_sql_result_to_feishu_if_needed(
        sql_result: SQLQueryResult,
        sql_description: str,
        user_info: UserInfo,
        upload_to_feishu: bool = False
) -> SQLQueryResult:
    """
    根据条件将SQL查询结果上传到飞书云文档，并调整展示的行数。

    如果查询结果行数超过MIN_ROWS_TO_IMPORT（默认20行），或用户强制上传（upload_to_feishu=True），则会将结果上传到飞书云文档。
    同时，根据内容大小动态计算需要在界面上展示的行数，避免内容过大导致性能问题：
    - 如果估算的内容大小超过MAX_CONTENT_TO_CUTOFF（默认16000字节），会动态计算合适的展示行数
    - 如果内容大小在限制范围内，则最多展示MIN_ROWS_TO_CUTOFF（默认400行）
    - 对于包含逗号、引号等特殊字符的数据，会进行正确的CSV转义处理

    Args:
        sql_result: SQL查询结果对象，包含列名和数据行
        sql_description: SQL查询的描述，用于生成飞书文档名称
        user_info: 用户信息，包含用户名和访问令牌等
        upload_to_feishu: 不管结果行数多少，是否强制上传到飞书，默认为False。如果用户明确要求将结果上传到飞书，则设置为True。

    Returns:
        SQLQueryResult: 处理后的SQL查询结果对象，可能包含截断的数据和飞书文档链接
    """
    columns = sql_result.columns
    rows = sql_result.data

    # 检查数据是否为空
    if not rows:
        sql_result.message = "查询结果为空。"
        sql_result.success = True
        return sql_result

    date_of_now = datetime.now().strftime("%Y-%m-%d")
    # 限制名称长度为100个字符以免超出限制
    name = f"{date_of_now}_{sql_description[:100]}"

    if upload_to_feishu or len(rows) >= min_rows:
        try:
            # 构建CSV内容，正确处理包含逗号的字符串
            def escape_csv_field(field):
                if field is None:
                    return ""
                field_str = str(field)
                # 如果字段包含逗号、引号或换行符，则需要用引号包围并处理内部引号
                if ',' in field_str or '"' in field_str or '\n' in field_str:
                    # 将字段中的引号替换为两个引号（CSV标准转义方式）
                    field_str = field_str.replace('"', '""')
                    # 用引号包围整个字段
                    return f'"{field_str}"'
                return field_str

            # 处理表头和数据行
            header_row = ",".join(map(escape_csv_field, columns))
            data_rows = [",".join(map(escape_csv_field, row)) for row in rows]

            # 动态计算需要截断的行数
            # 首先计算每行的平均大小
            total_rows = len(data_rows)
            if total_rows > 0:
                # 计算前100行（或全部行，如果少于100行）的平均大小
                sample_size = min(100, total_rows)
                sample_content = "\n".join(data_rows[:sample_size])
                avg_row_size = len(sample_content.encode('utf-8')) / sample_size

                # 估算全部内容的大小
                estimated_full_size = len(header_row.encode('utf-8')) + (avg_row_size * total_rows)

                # 如果估算大小超过最大限制，计算需要保留的行数
                display_rows = total_rows
                if estimated_full_size > max_content_size:
                    # 计算在最大内容大小限制下可以保留的行数
                    # 预留header的空间和一些缓冲区
                    header_size = len(header_row.encode('utf-8'))
                    available_size = max_content_size - header_size - 100  # 100字节作为缓冲区
                    display_rows = int(available_size / avg_row_size)

                    # 确保至少显示一些行，但不超过min_rows_to_cutoff
                    display_rows = max(10, min(display_rows, min_rows_to_cutoff))
                    logger.info(f"内容大小估算: {estimated_full_size}字节，超过限制{max_content_size}字节，将截断至{display_rows}行")
                else:
                    # 如果内容大小在限制范围内，使用默认的min_rows_to_cutoff
                    display_rows = min(min_rows_to_cutoff, total_rows)
                    logger.info(f"内容大小估算: {estimated_full_size}字节，在限制范围内，将显示{display_rows}行")
            else:
                display_rows = 0

            # 生成最终的CSV内容，并检查是否超过上传大小限制
            full_csv_content = "\n".join([header_row] + data_rows)
            csv_content_size_mb = len(full_csv_content.encode('utf-8')) / (1024 * 1024)

            # 如果CSV内容超过最大上传大小限制，需要截断数据行
            upload_rows = data_rows
            actual_total_rows = len(rows)
            uploaded_rows_count = actual_total_rows

            if csv_content_size_mb > max_upload_content_size_mb:
                logger.info(f"CSV内容大小: {csv_content_size_mb:.2f}MB，超过上传限制{max_upload_content_size_mb}MB，需要截断数据")

                # 计算header大小和每行平均大小
                header_size_bytes = len(header_row.encode('utf-8'))
                if len(data_rows) > 0:
                    # 使用前100行计算平均行大小，更准确
                    sample_size = min(100, len(data_rows))
                    sample_rows_content = "\n".join(data_rows[:sample_size])
                    avg_row_size_bytes = len(sample_rows_content.encode('utf-8')) / sample_size

                    # 计算在大小限制下可以上传的最大行数
                    max_upload_size_bytes = max_upload_content_size_mb * 1024 * 1024
                    available_size_for_rows = max_upload_size_bytes - header_size_bytes - 1024  # 预留1KB缓冲区
                    max_uploadable_rows = int(available_size_for_rows / avg_row_size_bytes)

                    # 确保至少上传一些行，但不超过总行数
                    max_uploadable_rows = max(1, min(max_uploadable_rows, len(data_rows)))
                    upload_rows = data_rows[:max_uploadable_rows]
                    uploaded_rows_count = max_uploadable_rows

                    logger.info(f"截断后将上传{uploaded_rows_count}行数据（共{actual_total_rows}行）")
                else:
                    upload_rows = []
                    uploaded_rows_count = 0

            # 生成用于上传的CSV内容
            csv_content = "\n".join([header_row] + upload_rows)

            # 获取用户的ChatBI文件夹token
            folder_token = None
            try:
                folder_token = DriveService.ensure_user_chatbi_folder(user_info.open_id)
                if folder_token:
                    logger.info(f"将在用户的ChatBI文件夹中创建文档: folder_token={folder_token}")
                else:
                    logger.warning(f"无法获取用户ChatBI文件夹，将在根目录创建文档")
            except Exception as e:
                logger.error(f"获取用户ChatBI文件夹时出错: {e}，将在根目录创建文档")
            
            # 验证access_token是否有效
            if not user_info.access_token or user_info.access_token.strip() == "":
                logger.error(f"用户access_token为空，无法创建飞书文档: user={user_info.user_name}, open_id={user_info.open_id}")
                sql_result.message = f"查询结果行数:{actual_total_rows}，但无法上传到飞书文档。\n\n**原因**: 您的飞书登录状态已过期（refresh token失效）。\n**解决方案**: 请重新登录飞书系统以重新授权，然后重试查询。\n\n💡 **提示**: 这通常发生在长时间未使用系统或飞书token过期时。"
                sql_result.error = "用户飞书登录状态过期，需要重新登录"
                return sql_result
            
            logger.info(f"即将为用户:{user_info.user_name}创建云文档:{name}, access_token:{user_info.access_token[:20]}..., open_id:{user_info.open_id}")
            result = await create_bitable_and_upload_csv(
                csv_content=csv_content,
                access_token=user_info.access_token,
                name=name,
                folder_token=folder_token,
            )

            # 截断数据以便展示
            sql_result.data = sql_result.data[:display_rows]

            if result and result.status == "success":
                # 根据是否截断了上传内容来生成不同的消息
                if uploaded_rows_count < actual_total_rows:
                    feishu_link_info = f"由于文件大小限制（{max_upload_content_size_mb}MB），已将前{uploaded_rows_count}条记录（共{actual_total_rows}条）上传至飞书文档：[{name}]({result.url})"
                    sql_result.message = f"查询结果行数:{actual_total_rows}，现展示了前{len(sql_result.data)}行。请务必以这种格式告知用户，否则用户无法获取完整数据：\n{feishu_link_info}"
                else:
                    feishu_link_info = f"完整的{actual_total_rows}条记录详情已经上传至飞书文档：[{name}]({result.url})"
                    sql_result.message = f"查询结果行数:{actual_total_rows}，现展示了前{len(sql_result.data)}行。请务必以这种格式告知用户，否则用户无法获取完整数据：\n{feishu_link_info}"

                sql_result.success = True
                sql_result.feishu_url = result.url
                sql_result.feishu_app_token = getattr(result, 'app_token', None)
                sql_result.feishu_table_id = getattr(result, 'table_id', None)
            else:
                sql_result.message = f"查询结果上传失败"
                sql_result.error = f"查询结果上传失败"
        except Exception as e:
            error_msg = str(e)
            logger.error(f"上传查询结果到飞书时出错: {error_msg}")

            # 检查是否是认证相关的错误
            if "飞书访问令牌无效或已过期" in error_msg or "Missing access token" in error_msg or "refresh token not found" in error_msg:
                sql_result.message = f"查询结果行数:{actual_total_rows}，但无法上传到飞书文档。\n\n**原因**: 您的飞书登录状态已过期（token失效）。\n**解决方案**: 请重新登录飞书系统以重新授权，然后重试查询。\n\n💡 **提示**: 这通常发生在长时间未使用系统或飞书token过期时。"
                sql_result.error = "飞书登录状态过期，需要重新登录"
            else:
                sql_result.message = f"查询结果行数:{actual_total_rows}，但上传过程中出错"
                sql_result.error = f"上传过程错误: {error_msg}"
    else:
        sql_result.message = f"查询结果行数:{len(rows)}，已经展示完毕。"
        sql_result.success = True

    return sql_result
